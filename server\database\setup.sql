-- PostgreSQL Database Setup for LabFalcon
-- Run this script to create the database and tables

-- Create database (run this as postgres superuser)
-- CREATE DATABASE labfalcon;

-- Connect to labfalcon database and run the following:

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL CHECK (LENGTH(name) >= 2),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL CHECK (LENGTH(password) >= 6),
    allow_validate_exam BOOLEAN DEFAULT FALSE,
    allow_handle_users BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON>reate index on email for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Create trigger to automatically update updated_at timestamp
CREATE OR R<PERSON>LACE FUNCTION update_updated_at_column()
<PERSON><PERSON><PERSON>NS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data (optional)
-- INSERT INTO users (name, email, password) VALUES 
-- ('John Doe', '<EMAIL>', '$2b$12$hashedpasswordhere'),
-- ('Jane Smith', '<EMAIL>', '$2b$12$anotherhashedpassword');

-- Verify tables
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';
