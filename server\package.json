{"name": "labfalcon", "version": "0.0.0", "private": true, "type": "module", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "seed": "NODE_ENV=development knex seed:run", "migrate": "NODE_ENV=development knex migrate:latest", "migrate:rollback": "NODE_ENV=development knex migrate:rollback"}, "dependencies": {"@arcjet/inspect": "^1.0.0-beta.8", "@arcjet/node": "^1.0.0-beta.8", "bcryptjs": "^3.0.2", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "debug": "~2.6.9", "dotenv": "^16.5.0", "express": "~4.16.1", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "morgan": "~1.9.1", "nodemailer": "^7.0.3", "pg": "^8.16.0", "react-to-print": "^3.1.1"}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/pg": "^8.15.4", "eslint": "^9.27.0", "globals": "^16.1.0", "nodemon": "^3.1.10"}}